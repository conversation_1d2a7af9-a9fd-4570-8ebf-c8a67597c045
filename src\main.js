/**
 * 主程序入口
 * 
 * 引导启动Nest应用实例
 */

import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './module';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, {
        abortOnError: false,                                // 是否出错时中止
    });
    // 获取配置服务
    const configService = app.get(ConfigService);
    // 获取启动配置
    const hostname = configService.get('listen.hostname');
    const port = configService.get('listen.port');
    // 启动服务器
    await app.listen(port, hostname);
}


module.exports = {
    bootstrap
};
  