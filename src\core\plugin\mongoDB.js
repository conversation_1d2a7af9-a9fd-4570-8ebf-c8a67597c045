/**
 * mongoDB数据库
 */

import { Module, Dependencies } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';

@Module({
    imports: [
        MongooseModule.forRootAsync({
            useFactory: async (configService) => ({
                uri: configService.get('mongoDB.url'),
                username: configService.get('mongoDB.username'),
                password: configService.get('mongoDB.password')
            }),
            inject: [ConfigService]
        })
    ]
})

export class mongoDBModule {}
