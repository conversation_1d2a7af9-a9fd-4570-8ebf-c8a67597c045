/**生产环境配置 */

// 项目启动端口号
const { RUN_PORT } = process.env;

// mongoDB数据库配置
const { MONGODB_HOST, MONGODB_PORT, MONGODB_DATABASE, MONGODB_USERNAME, MONGODB_PASSWORD } = process.env;

// Redis缓存配置
const { REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD, REDIS_CACHE_EXPIRE } = process.env;


// 启动配置
exports.listen = { 
    hostname: '0.0.0.0',
    port: +RUN_PORT
}

// mongoDB数据库配置
exports.mongoDB = { 
    url: `mongodb://${MONGODB_HOST}:${MONGODB_PORT}/${MONGODB_DATABASE}`,
    username: MONGODB_USERNAME,
    password: MONGODB_PASSWORD,
}
