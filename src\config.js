/**
 * 配置加载器
 */
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import defaultConfig from './config/config.default';                // 默认配置
import localConfig from './config/config.local';                    // 本地配置
import prodConfig from './config/config.prod';                      // 生产配置

// 获取环境变量
const env = process.env.NODE_ENV;
// 配置处理器
const configs = () => {
    // 根据环境变量加载不同配置
    switch (env) {
    case 'production':
        return { ...defaultConfig, ...prodConfig };
    default:
        return {...defaultConfig, ...localConfig};
    }
}

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            cache: true,
            load: [configs]
        })
    ],
})
    
export class ConfigsModule {}
