import js from "@eslint/js";
import globals from "globals";
import { defineConfig } from "eslint/config";
import babelParser from '@babel/eslint-parser';

export default defineConfig([
    {
        files: ["**/*.{js,mjs,cjs}"],
        plugins: { js },
        extends: ["js/recommended"],
        languageOptions: {
            parser: babelParser,
            globals: globals.node
        },
        rules: {
            "semi": "off",
            "quotes": "off",
            "indent": ["error", 4],
            "comma-dangle": "off",
            "arrow-parens": "off",
            "space-before-blocks": "off",
            "no-multi-spaces": "off",
            "prefer-const": "off",
            "no-trailing-spaces": "off",
            "no-multiple-empty-lines": "off",
            "array-bracket-spacing": "off",
            "spaced-comment": "off",
            "quote-props": "off",
            "no-else-return": "off",
            "eol-last": "off",
            "object-curly-spacing": "off",
            "no-unused-vars": "off",
            "space-infix-ops": "off",
            "space-before-function-paren": "off",
            "newline-per-chained-call": "off"
        }
    },
    {
        files: ["**/*.spec.js", "**/*.test.js", "**/test/**/*.js"],
        languageOptions: {
            globals: {
                ...globals.jest
            }
        }
    },
    {
        files: ["**/*.js"],
        rules: {
        },
        ignores: ["node_modules/**"]
    }
]);
